#!/bin/bash

# Reset Money Tracker App - Atlas Version (No local MongoDB)

echo "🔄 Resetting Money Tracker Application (MongoDB Atlas)..."

# Stop containers
echo "⏹️  Stopping containers..."
docker compose -f docker-compose-atlas.yml down

# Remove any orphaned containers
echo "🧹 Cleaning up orphaned containers..."
docker compose -f docker-compose-atlas.yml down --remove-orphans

# Rebuild the application image
echo "🔨 Rebuilding application image..."
docker build -t moneytracker:latest .

# Start containers
echo "🚀 Starting containers..."
docker compose -f docker-compose-atlas.yml up -d

# Wait a moment for containers to start
echo "⏳ Waiting for containers to start..."
sleep 15

# Check container status
echo "📊 Container status:"
docker compose -f docker-compose-atlas.yml ps

# Show logs
echo "📋 Recent logs:"
docker compose -f docker-compose-atlas.yml logs --tail=20

echo ""
echo "✅ Reset complete!"
echo ""
echo "🌐 Application should be available at: http://localhost:5000"
echo "🗄️  Using MongoDB Atlas (no local database)"
echo "🔧 If you still see session errors, visit: http://localhost:5000/clear-session"
echo ""
echo "💡 To clear browser data:"
echo "   1. Open browser developer tools (F12)"
echo "   2. Go to Application/Storage tab"
echo "   3. Clear all cookies and local storage for localhost:5000"
echo "   4. Refresh the page"
